import apiClient from "../apiClient";
import { useResponse, CreateUserPayload } from "../type/AuthUser";

export const Userapi = {
  getUser: async (): Promise<useResponse | null> => {
    try {
      const response = await apiClient.get("/users");
      return response.data;
    } catch (error) {
      console.error("Failed to fetch user:", error);
      return null;
    }
  },
  createUser: async (
    payload: CreateUserPayload
  ): Promise<CreateUserPayload | null> => {
    try {
      const response = await apiClient.post<CreateUserPayload>(
        "/users",
        payload
      );
      return response.data; // Assuming the user data is in the `data.data` field
    } catch (error) {
      console.error("Failed to create user:", error);
      return null;
    }
  },
};
