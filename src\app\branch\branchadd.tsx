import React, { useState, useCallback, useRef, useEffect } from "react";
import { MapPin, Plus, Save, Trash2 } from "lucide-react";
import ComponentCard from "../../components/common/ComponentCard";
import Label from "../../components/form/Label";
import Input from "../../components/form/input/InputField";
import { BranchData } from "../../service/type/Branch";

declare global {
  interface Window {
    L: any;
  }
}
const Branchadd = () => {
  const [branchData, setBranchData] = useState<BranchData[]>([]);
  const [selectedBranch, setSelectedBranch] = useState<BranchData | null>(null);
  const [isAddingBranch, setIsAddingBranch] = useState(false);
  const [mapLoaded, setMapLoaded] = useState(false);
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const markersRef = useRef<any[]>([]);
  const [formData, setFormData] = useState({
    name: "",
    address: "",
    longitude: "",
    latitude: "",
    radius: "50",
  });
  // Load Leaflet
  useEffect(() => {
    if (window.L) {
      setMapLoaded(true);
      return;
    }

    // Load Leaflet CSS
    const linkElement = document.createElement("link");
    linkElement.rel = "stylesheet";
    linkElement.href =
      "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.css";
    document.head.appendChild(linkElement);

    // Load Leaflet JS
    const scriptElement = document.createElement("script");
    scriptElement.src =
      "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.min.js";
    scriptElement.onload = () => {
      setMapLoaded(true);
    };
    document.head.appendChild(scriptElement);

    return () => {
      document.head.removeChild(linkElement);
      document.head.removeChild(scriptElement);
    };
  }, []);
  // Initialize map
  useEffect(() => {
    if (!mapLoaded || !mapRef.current || mapInstanceRef.current) return;

    // Sydney coordinates - you can change this to your preferred center
    const map = window.L.map(mapRef.current).setView([-33.8688, 151.2093], 13);

    // Add tile layer
    window.L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
      attribution: "© OpenStreetMap contributors",
    }).addTo(map);

    // Add click handler
    map.on("click", (e: any) => {
      if (isAddingBranch) {
        setFormData((prev) => ({
          ...prev,
          latitude: e.latlng.lat.toFixed(6),
          longitude: e.latlng.lng.toFixed(6),
        }));
      }
    });

    mapInstanceRef.current = map;

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [mapLoaded, isAddingBranch]);
  // Update markers when branches change
  useEffect(() => {
    if (!mapInstanceRef.current) return;

    // Clear existing markers
    markersRef.current.forEach((marker) => {
      mapInstanceRef.current.removeLayer(marker);
    });
    markersRef.current = [];

    // Add new markers
    branchData.forEach((branch) => {
      // Create marker
      const marker = window.L.marker([
        parseFloat(branch.latitude),
        parseFloat(branch.longitude),
      ]).addTo(mapInstanceRef.current).bindPopup(`
          <div>
            <h3 style="margin: 0 0 5px 0; font-weight: bold;">${
              branch.branch_name
            }</h3>
            <p style="margin: 0 0 5px 0; font-size: 12px;">${
              branch.branch_address
            }</p>
            <p style="margin: 0; font-size: 11px; color: #666;">
              ${parseFloat(branch.latitude).toFixed(4)}, ${parseFloat(
        branch.longitude
      ).toFixed(4)}
            </p>
          </div>
        `);

      // Add radius circle
      const circle = window.L.circle(
        [parseFloat(branch.latitude), parseFloat(branch.longitude)],
        {
          color: "#3b82f6",
          fillColor: "#3b82f6",
          fillOpacity: 0.1,
          radius: branch.radius,
        }
      ).addTo(mapInstanceRef.current);

      // Add click handler for editing
      marker.on("click", () => {
        handleEditBranch(branch);
      });

      markersRef.current.push(marker, circle);
    });
  }, [branchData]);

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSaveBranch = () => {
    if (
      !formData.name ||
      !formData.address ||
      !formData.latitude ||
      !formData.longitude
    ) {
      alert("Please fill in all fields");
      return;
    }

    const newBranch: BranchData = {
      branch_name: formData.name,
      branch_address: formData.address,
      location_name: formData.name, // Using name as location_name
      longitude: formData.longitude,
      latitude: formData.latitude,
      radius: parseFloat(formData.radius),
      user_ids: [],
    };

    if (selectedBranch) {
      setBranchData((prev) =>
        prev.map((branchData) =>
          branchData.branch_name === selectedBranch.branch_name
            ? { ...newBranch }
            : branchData
        )
      );
    } else {
      setBranchData((prev) => [...prev, newBranch]);
    }

    // Reset form
    setFormData({
      name: "",
      address: "",
      longitude: "",
      latitude: "",
      radius: "50",
    });
    setSelectedBranch(null);
    setIsAddingBranch(false);
  };

  const handleEditBranch = (branch: BranchData) => {
    setSelectedBranch(branch);
    setFormData({
      name: branch.branch_name,
      address: branch.branch_address,
      longitude: branch.longitude.toString(),
      latitude: branch.latitude.toString(),
      radius: branch.radius.toString(),
    });
    setIsAddingBranch(false);
  };

  const startAddingBranch = () => {
    setIsAddingBranch(true);
    setSelectedBranch(null);
    setFormData({
      name: "",
      address: "",
      longitude: "",
      latitude: "",
      radius: "50",
    });
  };

  const cancelAddingBranch = () => {
    setIsAddingBranch(false);
    setSelectedBranch(null);
    setFormData({
      name: "",
      address: "",
      longitude: "",
      latitude: "",
      radius: "50",
    });
  };

  const centerOnBranch = (branch: BranchData) => {
    if (mapInstanceRef.current) {
      mapInstanceRef.current.setView(
        [parseFloat(branch.latitude), parseFloat(branch.longitude)],
        15
      );
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-4 bg-gray-50 min-h-screen">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="p-4 bg-blue-600 text-white">
          <h1 className="text-2xl font-bold">Branch Location Manager</h1>
          <p className="text-blue-100">
            Click on the map to add new branch locations
          </p>
          {isAddingBranch && (
            <div className="mt-2 p-2 bg-blue-500 rounded text-sm">
              📍 Click anywhere on the map to set the branch location
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 p-6">
          {/* Map Area */}
          <div className="lg:col-span-2">
            <div className="relative">
              <div
                ref={mapRef}
                className={`w-full h-96 rounded-lg border-2 border-gray-300 ${
                  isAddingBranch ? "cursor-crosshair" : ""
                }`}
                style={{
                  backgroundColor: "#f0f9ff",
                  cursor: isAddingBranch ? "crosshair" : "default",
                }}
              >
                {!mapLoaded && (
                  <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                      <p className="text-gray-600">Loading map...</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Map controls */}
              <div className="flex justify-between items-center mt-4">
                <button
                  onClick={startAddingBranch}
                  disabled={isAddingBranch || !mapLoaded}
                  className="flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:bg-gray-400 transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  Add Branch
                </button>

                {isAddingBranch && (
                  <button
                    onClick={cancelAddingBranch}
                    className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
                  >
                    Cancel
                  </button>
                )}

                <div className="text-sm text-gray-600">
                  {mapLoaded
                    ? `${branchData.length} branches`
                    : "Map loading..."}
                </div>
              </div>
            </div>
          </div>

          {/* Form Area */}
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-lg font-semibold mb-4">
                {selectedBranch ? "Edit Branch" : "Branch Details"}
              </h2>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Branch Name
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="Enter branch name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Branch Address
                  </label>
                  <input
                    type="text"
                    value={formData.address}
                    onChange={(e) =>
                      handleInputChange("address", e.target.value)
                    }
                    placeholder="Enter full address"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Longitude
                    </label>
                    <input
                      type="number"
                      step="any"
                      value={formData.longitude}
                      onChange={(e) =>
                        handleInputChange("longitude", e.target.value)
                      }
                      placeholder="Click map"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Latitude
                    </label>
                    <input
                      type="number"
                      step="any"
                      value={formData.latitude}
                      onChange={(e) =>
                        handleInputChange("latitude", e.target.value)
                      }
                      placeholder="Click map"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Radius (meters)
                  </label>
                  <input
                    type="number"
                    value={formData.radius}
                    onChange={(e) =>
                      handleInputChange("radius", e.target.value)
                    }
                    placeholder="Service radius"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <button
                  onClick={handleSaveBranch}
                  className="w-full flex items-center justify-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Save className="w-4 h-4" />
                  {selectedBranch ? "Update Branch" : "Save Branch"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Branchadd;
